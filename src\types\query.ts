import { IdentityType } from '../config/trading';

/**
 * 交易数据，HTTP查询选项，条件二选一
 */
export interface QueryOptions {
  /** 产品、策略、账号的标识 ID */
  identityId?: number | string;
  /** 母单ID */
  parentOrderId?: number | string;
}

/**
 * 交易数据，Socket查询选项，条件三选一
 */
export interface SocketQueryOptions extends QueryOptions {
  /** 多个账号ID */
  accountIds?: Array<number | string>;
}

/**
 * 交易历史数据，综合查询选项
 */
export interface HistoryQueryOptions {
  /** 产品ID、策略ID、账号ID，三选一 */
  fund_id?: number | string;
  /** 产品ID、策略ID、账号ID，三选一 */
  strategy_id?: number | string;
  /** 产品ID、策略ID、账号ID，三选一 */
  account_id?: number | string;
  /** 第几页 */
  pageNo: number;
  /** 分页大小 */
  pageSize: number;
  /** 开始日期，yyyyMMdd格式 */
  begin_day: number | string;
  /** 结束日期，yyyyMMdd格式 */
  end_day: number | string;
  /** 指定合约代码 */
  instrument?: string;
  /** 交易标识位 */
  business_flag?: number | string;
}

/**
 * 权益历史数据，综合查询选项
 */
export interface EquityHistoryQueryOptions {
  /** 产品ID、策略ID、账号ID， */
  identity_id: number | string;
  /** 产品、策略、账号的类型 */
  identity_type: IdentityType;
  /** 第几页 */
  pageNo: number;
  /** 分页大小 */
  pageSize: number;
  /** 开始日期，yyyyMMdd格式 */
  begin_day: number | string;
  /** 结束日期，yyyyMMdd格式 */
  end_day: number | string;
}
