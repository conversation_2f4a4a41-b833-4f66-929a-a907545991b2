html {
  --g-bg: #000;
  --g-border: rgba(54, 59, 100, 1);
  --g-border-color-1: rgba(209, 233, 255, 0.6);
  --g-block-bg-1: #2c2c35;
  --g-block-bg-2: #1e1e24;
  --g-block-bg-3: #61a6fa;
  --g-block-bg-4: #7ab7ff;
  --g-block-bg-5: #070707;
  --g-block-bg-6: #40404a;
  --g-block-bg-7: #434343;
  --g-block-bg-8: #3506ef;
  --g-block-bg-9: #384cff;
  --g-block-bg-10: #272727;
  --g-block-bg-11: #3b00f3;
  --g-block-bg-12: #4807ea;
  --g-block-bg-13: #178bff;
  --g-block-bg-14: #1570ef;
  --g-bg-hover-1: rgba(99, 107, 146, 0.7);
  --g-bg-hover-2: rgba(99, 107, 146, 0.3);
  --g-bg-hover-3: rgba(91, 91, 101, 1);
  --g-bg-hover-4: rgba(91, 91, 101, 0.6);
  --g-bg-hover-5: rgba(33, 33, 33, 0.3);
  --g-panel-bg: rgb(42, 45, 62);
  --g-panel-bg2: rgb(35, 38, 56);
  --g-panel-bg3: rgba(57, 63, 95, 1);
  --g-text-color-1: #8a8a98;
  --g-text-color-2: #f5f5ff;
  --g-text-color-3: #70707c;
  --g-text-color-4: #757889;
  --g-text-color-5: #a9a9b7;
  --g-text-color-6: #d0d0da;
  --g-text-color-7: #ffffffb2;
  --g-header-bg: #1c3253;
  --g-panel-text: #a9a9b7;
  --g-header-text: rgba(155, 158, 189, 1);
  --g-primary: rgb(22, 98, 249);
  --g-danger: #fb484b;
  --g-hover: rgba(29, 35, 42, 0.5);
  --g-hover-row: rgba(44, 110, 177, 0.5);
  --g-white: rgba(255, 255, 255, 1);
  --g-red: #fd4438;
  --g-green: #3dd598;
  --g-orange: rgba(255, 153, 0, 1);
  --g-bg-red: #ff0041;
  --g-bg-red-hover: #ff4d7a;
  --g-bg-green: rgb(56, 142, 60);
  --g-bg-green-hover: rgb(116, 176, 119);
  --g-input-shadow: 0 0 0 1px var(--g-bg);
  --g-bg-1: rgb(10, 10, 10);
  --g-active: rgba(53, 6, 239, 1);
  --g-table-odd-row-bg: #1e1e24;
  --g-table-even-row-bg: #050505;
  --g-el-scrollbar-thumb-bg: rgba(163, 166, 173, 0.6);
  --g-native-scrollbar-thumb-bg: rgba(163, 166, 173, 0.2);
  --g-native-hover-scrollbar-thumb-bg: rgba(163, 166, 173, 0.4);

  --g-active-row: #40404a;
  --g-form-icon-filter: brightness(0) saturate(100%) invert(68%) sepia(9%) saturate(476%)
    hue-rotate(201deg) brightness(90%) contrast(90%);
}
