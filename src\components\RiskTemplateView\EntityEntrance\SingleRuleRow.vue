<script setup lang="tsx">
import { onMounted } from 'vue';
import type { RiskRule } from '../../../../../xtrade-sdk/dist';
import { translateCheckObject } from '../IndicatorTreeNodeLogic';

const { rules } = defineProps<{
  rules: RiskRule[];
}>();

function formatCheckObject(checkObject: number) {
  const objects = translateCheckObject(checkObject);
  return objects.length == 0 ? 'N/A' : objects.map(x => x.label).join(',');
}

defineExpose({});

onMounted(() => {
  //
});
</script>

<template>
  <div v-if="rules.length == 0" lh-40 text-center>
    <span>当前无指标</span>
  </div>
  <div v-else class="rule-list">
    <div v-for="rule in rules" :key="rule.id" class="each-rule-row">
      <div w-200>{{ rule.ruleName }}</div>
      <div w-200 flex-1>{{ JSON.stringify(rule.configuration) }}</div>
      <div v-300>
        <span>针对</span>
        <span>{{ formatCheckObject(rule.checkObject) }}</span>
        <span>{{ rule.checkInterval }}</span>
        <span>秒执行一次 |</span>
        <span>{{ rule.beginDay }} ~ {{ rule.endDay || '至今' }}</span>
        <span>| 每日</span>
        <span>{{ rule.beginTime }} ~ {{ rule.endTime }}</span>
        <span>执行</span>
      </div>
      <div w-100 flex gap-10>
        <el-link underline>编辑</el-link>
        <el-link underline>解绑</el-link>
      </div>
    </div>
  </div>
</template>

<style scoped>
.rule-list {
  display: flex;
  align-items: center;

  .each-rule-row {
    display: flex;
    align-items: center;
    gap: 30px;
  }
}
</style>
