import type { CustomColumn } from '@/types';
import { OrderStatusEnum, PositionEffectEnum, TradeDirectionEnum, AssetTypeEnum } from '@/enum';
import {
  formatEnum,
  enumCol,
  defaultCol,
  thousandsCol,
  thousandsIntCol,
  timestampCol,
  formatNumber,
  formatDateTime,
  thousands,
  thousandsInt,
  navCol,
} from '@/script/formatter';
import {
  BusinessFlagEnum,
  type OrderInfo,
  type PositionInfo,
  type TradeRecordInfo,
  type EquityInfo,
  type CashInfo,
} from '../../../../../../xtrade-sdk/dist';

// 订单状态颜色函数
const getOrderStatusColor = (value: number): string => {
  switch (value) {
    case OrderStatusEnum.全成:
      return 'c-[var(--g-green)]';
    case OrderStatusEnum.已撤:
    case OrderStatusEnum.已驳回:
    case OrderStatusEnum.废单:
      return 'c-[var(--g-red)]';
    case OrderStatusEnum.部分成交:
    case OrderStatusEnum.部分成交撤单:
      return 'c-[var(--g-orange)]';
    default:
      return 'c-[var(--g-white)]';
  }
};

// 通用列定义
export const commonColumns: Record<string, CustomColumn<any>> = {
  instrument: {
    key: 'instrument',
    dataKey: 'instrument',
    title: '代码',
    width: 150,
    sortable: true,
  },
  instrumentName: {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '名称',
    width: 160,
    sortable: true,
  },
  direction: {
    key: 'direction',
    dataKey: 'direction',
    title: '方向',
    width: 140,
    sortable: true,
    cellRenderer: (params: any) => enumCol(params, TradeDirectionEnum, { color: {} }),
    textRenderer: (cellData: any) => formatEnum(cellData, TradeDirectionEnum),
  },
  positionEffect: {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '开平',
    width: 120,
    sortable: true,
    cellRenderer: (params: any) => enumCol(params, PositionEffectEnum),
    textRenderer: (cellData: any) => formatEnum(cellData, PositionEffectEnum),
  },
  assetType: {
    key: 'assetType',
    dataKey: 'assetType',
    title: '资产类型',
    width: 200,
    sortable: true,
    cellRenderer: (params: any) => enumCol(params, AssetTypeEnum),
    textRenderer: (cellData: any) => formatEnum(cellData, AssetTypeEnum),
  },
  tradingDay: {
    key: 'tradingDay',
    dataKey: 'tradingDay',
    title: '交易日',
    width: 200,
    sortable: true,
  },
  accountName: {
    key: 'accountName',
    dataKey: 'accountName',
    title: '账号名称',
    width: 160,
    sortable: true,
  },
  userName: {
    key: 'userName',
    dataKey: 'userName',
    title: '发起人',
    width: 180,
    sortable: true,
  },
  tradedPrice: {
    key: 'tradedPrice',
    dataKey: 'tradedPrice',
    title: '成交价',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: defaultCol,
    textRenderer: val => formatNumber(val),
  },
  tradeTime: {
    key: 'tradeTime',
    dataKey: 'tradeTime',
    title: '成交时间',
    width: 200,
    sortable: true,
  },
  exchangeOrderId: {
    key: 'exchangeOrderId',
    dataKey: 'exchangeOrderId',
    title: '报单编号',
    width: 200,
    sortable: true,
  },
  marketValue: {
    key: 'marketValue',
    dataKey: 'marketValue',
    title: '市值',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  frozenMargin: {
    key: 'frozenMargin',
    dataKey: 'frozenMargin',
    title: '冻结资金',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  closeProfit: {
    key: 'closeProfit',
    dataKey: 'closeProfit',
    title: '平仓盈亏',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: params => thousandsCol(params, { color: {} }),
    textRenderer: thousands,
  },
  commission: {
    key: 'commission',
    dataKey: 'commission',
    title: '手续费',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  inMoney: {
    key: 'inMoney',
    dataKey: 'inMoney',
    title: '入金',
    width: 200,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  outMoney: {
    key: 'outMoney',
    dataKey: 'outMoney',
    title: '出金',
    width: 200,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  createTime: {
    key: 'createTime',
    dataKey: 'createTime',
    title: '创建时间',
    width: 200,
    sortable: true,
    cellRenderer: params => timestampCol(params, 'hh:mm:ss'),
    textRenderer: val => formatDateTime(val, 'hh:mm:ss'),
  },
};

// 共享的订单列定义
export const orderColumns: Record<keyof OrderInfo | string, CustomColumn<OrderInfo>> = {
  ...commonColumns,
  orderStatus: {
    key: 'orderStatus',
    dataKey: 'orderStatus',
    title: '订单状态',
    width: 200,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const colorClass = getOrderStatusColor(cellData);
      return <span class={colorClass}>{OrderStatusEnum[cellData]}</span>;
    },
    textRenderer: val => formatEnum(val, OrderStatusEnum),
  },
  businessFlag: {
    key: 'businessFlag',
    dataKey: 'businessFlag',
    title: '交易方式',
    width: 200,
    sortable: true,
    cellRenderer: params => enumCol(params, BusinessFlagEnum),
    textRenderer: cellData => formatEnum(cellData, BusinessFlagEnum),
  },
  orderPrice: {
    key: 'orderPrice',
    dataKey: 'orderPrice',
    title: '委托价',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: defaultCol,
    textRenderer: val => formatNumber(val),
  },
  volumeOriginal: {
    key: 'volumeOriginal',
    dataKey: 'volumeOriginal',
    title: '委托量',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsIntCol,
    textRenderer: thousandsInt,
  },
  tradedVolume: {
    key: 'tradedVolume',
    dataKey: 'tradedVolume',
    title: '成交量',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsIntCol,
    textRenderer: thousandsInt,
  },

  orderTime: {
    key: 'orderTime',
    dataKey: 'orderTime',
    title: '报单时间',
    width: 160,
    sortable: true,
  },
  fundName: {
    key: 'fundName',
    dataKey: 'fundName',
    title: '产品',
    width: 150,
    sortable: true,
  },
  foreign: {
    key: 'foreign',
    dataKey: 'foreign',
    title: '外来单',
    width: 180,
    sortable: true,
  },
  forceClose: {
    key: 'forceClose',
    dataKey: 'forceClose',
    title: '强平',
    width: 160,
    sortable: true,
  },
  remark: {
    key: 'remark',
    dataKey: 'remark',
    title: '备注',
    width: 150,
    sortable: true,
  },
};

// 共享的持仓列定义
export const positionColumns: Record<keyof PositionInfo | string, CustomColumn<PositionInfo>> = {
  ...commonColumns,
  yesterdayPosition: {
    key: 'yesterdayPosition',
    dataKey: 'yesterdayPosition',
    title: '昨仓',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsIntCol,
    textRenderer: thousandsInt,
  },
  todayPosition: {
    key: 'todayPosition',
    dataKey: 'todayPosition',
    title: '今仓',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsIntCol,
    textRenderer: thousandsInt,
  },
  frozenVolume: {
    key: 'frozenVolume',
    dataKey: 'frozenVolume',
    title: '冻结仓数',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsIntCol,
    textRenderer: thousandsInt,
  },
  avgPrice: {
    key: 'avgPrice',
    dataKey: 'avgPrice',
    title: '持仓均价',
    width: 160,
    align: 'right',
    cellRenderer: defaultCol,
    textRenderer: val => formatNumber(val),
  },

  floatProfit: {
    key: 'floatProfit',
    dataKey: 'floatProfit',
    title: '浮动盈亏',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: params => thousandsCol(params, { color: {} }),
    textRenderer: thousands,
  },

  usedCommission: {
    key: 'usedCommission',
    dataKey: 'usedCommission',
    title: '手续费',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  usedMargin: {
    key: 'usedMargin',
    dataKey: 'usedMargin',
    title: '保证金',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  updateTime: {
    key: 'updateTime',
    dataKey: 'updateTime',
    title: '更新时间',
    width: 200,
    sortable: true,
    cellRenderer: params => timestampCol(params, 'hh:mm:ss'),
    textRenderer: val => formatDateTime(val, 'hh:mm:ss'),
  },
};

// 共享的成交记录列定义
export const tradeRecordColumns: Record<
  keyof TradeRecordInfo | string,
  CustomColumn<TradeRecordInfo>
> = {
  ...commonColumns,
  volume: {
    key: 'volume',
    dataKey: 'volume',
    title: '成交量',
    width: 160,
    sortable: true,
    align: 'right',
    cellRenderer: defaultCol,
    textRenderer: val => formatNumber(val, { fix: 0 }),
  },
  tradeId: {
    key: 'tradeId',
    dataKey: 'tradeId',
    title: '成交编号',
    width: 150,
    sortable: true,
  },
};

// 共享的权益列定义
export const equityColumns: Record<keyof EquityInfo | string, CustomColumn<EquityInfo>> = {
  ...commonColumns,
  balance: {
    key: 'balance',
    dataKey: 'balance',
    title: '权益',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  available: {
    key: 'available',
    dataKey: 'available',
    title: '可用资金',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  preBalance: {
    key: 'preBalance',
    dataKey: 'preBalance',
    title: '昨日权益',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  risePercent: {
    key: 'risePercent',
    dataKey: 'risePercent',
    title: '涨跌幅(%)',
    width: 200,
    sortable: true,
    align: 'right',
  },
  positionProfit: {
    key: 'positionProfit',
    dataKey: 'positionProfit',
    title: '浮动盈亏(￥)',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: (params: any) => thousandsCol(params, { color: {} }),
    textRenderer: thousands,
  },

  margin: {
    key: 'margin',
    dataKey: 'margin',
    title: '占用保证金',
    width: 200,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  fundShare: {
    key: 'fundShare',
    dataKey: 'fundShare',
    title: '份额',
    width: 200,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  nav: {
    key: 'nav',
    dataKey: 'nav',
    title: '净值',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: navCol,
    textRenderer: (val: any) => formatNumber(val, { fix: 4 }),
  },
  dayProfit: {
    key: 'dayProfit',
    dataKey: 'dayProfit',
    title: '收益',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: (params: any) => thousandsCol(params, { color: {} }),
    textRenderer: thousands,
  },
};

export const cashRecordColumns: Record<keyof CashInfo | string, CustomColumn<CashInfo>> = {
  ...commonColumns,
  identityName: {
    key: 'identityName',
    dataKey: 'identityName',
    title: '账号名称',
    width: 200,
    sortable: true,
  },
  operatorUserName: {
    key: 'operatorUserName',
    dataKey: 'operatorUserName',
    title: '操作员',
    width: 160,
    sortable: true,
  },
  type: {
    key: 'type',
    dataKey: 'type',
    title: '备注',
    width: 160,
    sortable: true,
    cellRenderer: ({ cellData }) => (cellData === 1 ? <div>分红入金</div> : <div>---</div>),
    textRenderer: cellData => (cellData === 1 ? '分红入金' : '---'),
  },
};
