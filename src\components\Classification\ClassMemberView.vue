<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import AddMembersDialog from './AddMembersDialog.vue';
import ChooseFundDialog from '../common/EntityChooseDialog/ChooseFundDialog.vue';
import ChooseAccountDialog from '../common/EntityChooseDialog/ChooseAccountDialog.vue';
import { computed, reactive, ref, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder, ElMessage } from 'element-plus';
import type { ColumnDefinition, RowAction, AccountInfo, ProductInfo } from '@/types';
import { findNearestValue, formatDateTime, max, remove } from '@/script';
import { deleteConfirm } from '@/script/interaction';

import {
  Repos,
  type TradeClassificationMember,
  TradeClassificationType,
} from '../../../../xtrade-sdk/dist';

interface CellRenderParam {
  rowData: TradeClassificationMember;
  cellData: any;
}

const { classType, classId, className } = defineProps<{
  classType: TradeClassificationType;
  classId: number | null;
  className: string | null;
}>();

const memberName = computed(() => {
  return classType == TradeClassificationType.Asset
    ? '合约名称'
    : classType == TradeClassificationType.ProductGroup
      ? '产品名称'
      : '账号名称';
});

const memberCode = computed(() => {
  return classType == TradeClassificationType.Asset
    ? '合约代码'
    : classType == TradeClassificationType.ProductGroup
      ? '产品ID'
      : '账号ID';
});

const repoInstance = new Repos.ClassificationRepo();

// 基础列定义
const columns = computed<ColumnDefinition<TradeClassificationMember>>(() => {
  return [
    { key: 'memberName', title: memberName.value, width: 200, minWidth: 200 },
    { key: 'memberCode', title: memberCode.value, width: 150, minWidth: 150 },
    { key: 'createTime' as any, title: '绑定时间', width: 100, cellRenderer: formatDate },
  ];
});

// 行操作
const rowActions: RowAction<TradeClassificationMember>[] = [
  {
    label: '上移',
    icon: 'arrow-up',
    show: isMovingAllowed,
    onClick: row => {
      moveUp(row);
    },
  },
  {
    label: '下移',
    icon: 'arrow-down',
    show: isMovingAllowed,
    onClick: row => {
      moveDown(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    show: isEditAllowed,
    onClick: row => {
      deleteRow(row);
    },
  },
];

const records = ref<TradeClassificationMember[]>([]);
const tableRef = useTemplateRef('tableRef');
const addMembersDialogRef = useTemplateRef('addMembersDialogRef');

// 对话框相关
const dialogVisible = ref(false);
const assignedOrder = ref(0);

watch(
  () => classId,
  () => {
    request();
  },
  { immediate: true },
);

const is4Asset = computed(() => {
  return classType == TradeClassificationType.Asset;
});

const is4Product = computed(() => {
  return classType == TradeClassificationType.ProductGroup;
});

const is4Account = computed(() => {
  return classType == TradeClassificationType.AccountGroup;
});

const addButtonText = computed(() => {
  return is4Product.value ? '维护产品联合组' : is4Account.value ? '维护账户组' : '挂载';
});

function isMovingAllowed() {
  return records.value.length > 1;
}

function isEditAllowed() {
  // return data.orgId != -1;
  return true;
}

function formatDate(params: CellRenderParam) {
  return <span>{formatDateTime(params.cellData)}</span>;
}

// 新建分类成员
const handleCreate = () => {
  if (is4Asset.value) {
    const current_max = records.value.length == 0 ? 0 : max(records.value.map(x => x.sortOrder));
    assignedOrder.value = current_max + 1;
    dialogVisible.value = true;
  } else if (is4Product.value) {
    ShowBindingFundsDialog();
  } else {
    ShowBindingAccountsDialog();
  }
};

// 上移
function moveUp(row: TradeClassificationMember) {
  moveRow(row, 1);
}

// 下移
function moveDown(row: TradeClassificationMember) {
  moveRow(row, -1);
}

async function moveRow(row: TradeClassificationMember, direciton: number) {
  const beyonds =
    direciton > 0
      ? records.value.filter(item => item.sortOrder > row.sortOrder)
      : records.value.filter(item => item.sortOrder < row.sortOrder);

  if (beyonds.length == 0) {
    return ElMessage.info('当前已是最大排序');
  }

  const nearest = findNearestValue(beyonds, row.sortOrder, x => x.sortOrder)!;
  const t = nearest.sortOrder;
  nearest.sortOrder = row.sortOrder;
  row.sortOrder = t;

  const resp = await repoInstance.updateTradeClassificationMember(classId!, row);
  const resp2 = await repoInstance.updateTradeClassificationMember(classId!, nearest);
  const { errorCode, errorMsg } = resp;
  const { errorCode: errorCode2, errorMsg: errorMsg2 } = resp2;

  if (errorCode == 0 && errorCode2 == 0) {
    ElMessage.success('移动成功');
    remove(records.value, x => x.id == row.id);
  } else {
    ElMessage.error(`移动失败：${errorCode || errorCode2}/${errorMsg || errorMsg2}`);
  }
}

// 删除分类成员
async function deleteRow(row: TradeClassificationMember) {
  const result = await deleteConfirm('删除确认', `是否删除分类成员： ${row.memberName}？`);
  if (result !== true) {
    return;
  }

  const resp = await repoInstance.deleteTradeClassificationMember(row.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已删除');
    remove(records.value, x => x.id == row.id);
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  if (!classId) {
    records.value = [];
    return;
  }

  const list = (await repoInstance.getTradeClassificationMembers(classId)).data || [];
  records.value = list;
}

const dialogFund = reactive({
  visible: false,
  title: '',
  targets: [] as string[], // 已选中的ID列表
});

const dialogAccount = reactive({
  visible: false,
  title: '',
  targets: [] as string[], // 已选中的ID列表
});

const ShowBindingFundsDialog = () => {
  const dialog = dialogFund;
  dialog.title = '产品联合组管理';
  dialog.targets = records.value.map(x => x.memberCode);
  dialog.visible = true;
};

const ShowBindingAccountsDialog = () => {
  const dialog = dialogAccount;
  dialog.title = '账号组管理';
  dialog.targets = records.value.map(x => x.memberCode);
  dialog.visible = true;
};

const extractChoosed2Members = (
  selectedItems: (ProductInfo | AccountInfo)[],
): TradeClassificationMember[] => {
  if (is4Product.value) {
    return selectedItems.map(x => {
      const { id, fundName } = x as ProductInfo;
      const item: TradeClassificationMember = {
        id: null as any,
        classificationId: classId!,
        memberType: classType,
        memberCode: id,
        memberName: fundName,
        sortOrder: 0,
      };
      return item;
    });
  } else {
    return selectedItems.map(x => {
      const { id, accountName } = x as AccountInfo;
      const item: TradeClassificationMember = {
        id: null as any,
        classificationId: classId!,
        memberType: classType,
        memberCode: id,
        memberName: accountName,
        sortOrder: 0,
      };
      return item;
    });
  }
};

const hideBindingFundsDialog = () => {
  const dialog = dialogFund;
  dialog.visible = false;
  dialog.targets = [];
};

const hideBindingAccountsDialog = () => {
  const dialog = dialogAccount;
  dialog.visible = false;
  dialog.targets = [];
};

const confirmBinding = async (selectedItems: (ProductInfo | AccountInfo)[]) => {
  // 最新选择的
  const choosed = extractChoosed2Members(selectedItems);
  const list = records.value;
  const expired = list.filter(x => !choosed.some(y => y.memberCode == x.memberCode));
  const intersections = list.filter(x => choosed.some(y => y.memberCode == x.memberCode));
  const comers = choosed.filter(x => !list.some(y => y.memberCode == x.memberCode));

  // 为新增设置排序序号
  const start_order = intersections.length + 1;
  comers.forEach((x, i) => {
    x.sortOrder = start_order + i;
  });

  if (expired.length > 0) {
    await repoInstance.deleteTradeClassificationMember(expired.map(x => x.id));
  }

  if (comers.length > 0) {
    for (let i = 0; i < comers.length; i++) {
      await repoInstance.createTradeClassificationMember(classId!, comers[i]);
    }
  }

  hideBindingFundsDialog();
  hideBindingAccountsDialog();
  ElMessage.success(`分组成员已保存，新增个数 = ${comers.length}，删除个数 = ${expired.length}`);
  request();
};
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'sortOrder', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="200"
    select
    fixed
    show-index
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button type="primary" @click="handleCreate">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>{{ addButtonText }}</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 分类成员编辑对话框 -->
  <AddMembersDialog
    ref="addMembersDialogRef"
    v-model="dialogVisible"
    :class-type="classType"
    :class-id="classId"
    :class-name="className"
    :assigned-order="assignedOrder"
    @success="handleDialogSuccess"
  />

  <!-- 绑定产品对话框 -->
  <ChooseFundDialog
    v-model="dialogFund.visible"
    :title="dialogFund.title"
    :selected-ids="dialogFund.targets"
    @confirm="confirmBinding"
  />

  <!-- 绑定账号对话框 -->
  <ChooseAccountDialog
    v-model="dialogAccount.visible"
    :title="dialogAccount.title"
    :selected-ids="dialogAccount.targets"
    @confirm="confirmBinding"
  />
</template>

<style scoped></style>
