/**
 * 交易动态指标接口
 * 用于定义可动态配置的交易相关指标，支持条件判断和全局应用
 */
export interface TradeDynamicIndicator {
  /**
   * 指标的唯一标识ID
   * 使用可为 null 的 number 类型对应 Java 的 Long（非基本类型）
   */
  id: number;

  /**
   * 动态指标的类型枚举值
   */
  conditionType: number;

  /**
   * 参数值
   * 指标的具体数值，用于比较或计算
   */
  value: number;

  /**
   * 创建人ID
   * 标识创建该指标的用户ID
   */
  creatorId: number;

  /**
   * 运算符类型
   * 表示条件表达式中的比较操作，如：1-大于，2-等于，3-小于等（具体值需根据 ExpressionType 枚举定义）
   */
  expressionType: number;

  /**
   * 更新时间
   * ISO 8601 格式的时间字符串，例如：'2025-09-07T14:14:00Z'
   * 对应 Java 的 Timestamp 类型
   */
  updateTime: number;

  /**
   * 条件字段列名
   * 指明该指标应用于数据库或数据模型中的哪一个字段/列
   */
  conditionColumn: string;
}