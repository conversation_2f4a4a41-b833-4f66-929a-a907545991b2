<script setup lang="ts">
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import { computed, ref, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';

import {
  type InstrumentInfo,
  Repos,
  type TradeClassificationMember,
  TradeClassificationType,
} from '../../../../xtrade-sdk/dist';

const { classType, classId, className, assignedOrder } = defineProps<{
  classType: TradeClassificationType;
  classId: number | null;
  className: string | null;
  assignedOrder: number;
}>();

const is4Asset = computed(() => {
  return classType == TradeClassificationType.Asset;
});

const is4Product = computed(() => {
  return classType == TradeClassificationType.ProductGroup;
});

const is4Account = computed(() => {
  return classType == TradeClassificationType.AccountGroup;
});

const title = computed(() => {
  return is4Asset.value ? '添加成员' : is4Product.value ? '添加产品' : '添加账号';
});

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

const rules = computed(() => {
  if (is4Asset.value) {
    return {
      memberCode: [{ required: true, message: '请输入合约代码', trigger: 'blur' }],
      memberName: [{ required: true, message: '请输入合约名称', trigger: 'blur' }],
    };
  } else if (is4Product.value) {
    return {
      memberCode: [{ required: true, message: '请输入产品代码', trigger: 'blur' }],
      memberName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
    };
  } else if (is4Account.value) {
    return {
      memberCode: [{ required: true, message: '请输入代码', trigger: 'blur' }],
      memberName: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
    };
  } else {
    return {};
  }
});

const formRef = useTemplateRef('formRef');
const form = ref<TradeClassificationMember>(createEmpty());
const selectedInstrument = ref<InstrumentInfo>();

watch(
  () => selectedInstrument.value,
  ins => {
    form.value.memberCode = ins?.instrument || '';
    form.value.memberName = ins?.instrumentName || '';
  },
);

function createEmpty(): TradeClassificationMember {
  const item = {
    id: null as any,
    classificationId: 0,
    memberType: TradeClassificationType.Asset,
    memberCode: '',
    memberName: '',
    sortOrder: 0,
  };

  item.classificationId = classId || 0;
  item.memberType = classType;
  item.sortOrder = assignedOrder;
  return item;
}

// 监听visible变化
watch(visible, val => {
  if (val) {
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  form.value = createEmpty();
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      handleSave();
    }
  });
};

const repoInstance = new Repos.ClassificationRepo();

async function handleSave() {
  const resp = await repoInstance.createTradeClassificationMember(classId!, form.value);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success(`已保存`);
    emit('success');
    handleClose();
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
}
</script>

<template>
  <el-dialog :model-value="visible" :title="title" width="500px" @close="handleClose" draggable>
    <el-form
      ref="formRef"
      class="typical-form"
      :model="form"
      :rules="rules"
      label-position="top"
      label-width="80px"
    >
      <el-form-item label="所属分类">
        <el-input v-bind:value="className" disabled>
          <template #prefix>
            <i class="iconfont icon-block"></i>
          </template>
        </el-input>
      </el-form-item>
      <template v-if="is4Asset">
        <el-form-item label="搜索合约">
          <InstrumentInput v-model="selectedInstrument" placeholder="请输入合约代码或名称" w-full />
        </el-form-item>
        <el-form-item label="合约名称" prop="memberName">
          <el-input v-model="form.memberName" placeholder="请输入合约名称" clearable>
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="合约代码" prop="memberCode">
          <el-input v-model="form.memberCode" placeholder="请输入合约代码" clearable>
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped></style>
