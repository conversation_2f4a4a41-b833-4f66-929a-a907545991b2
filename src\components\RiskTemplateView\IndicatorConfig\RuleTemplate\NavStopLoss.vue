<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { deepClone } from '@/script';
import type { AnyIndicatorRiskParamObject, CommonRiskAlertConfig } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';
import { AlertType, ExpressionType, NavValueStopLossType } from '@/enum/riskc';

const applicationExpressions = [
  { label: '不限', value: 0 },
  ExpressionType.GreaterThan,
  ExpressionType.GreaterEqual,
  ExpressionType.LessThan,
  ExpressionType.LessEqual,
];

const applicationAlertTypes = [AlertType.Warning, AlertType.Prevention, AlertType.Ignore];

/**
 * 净值止损
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject, CommonRiskAlertConfig {
  netValueStopLossType: number;
}

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>({
  classType: IdcComponentNameDef.TradeFrequency,
  alertType: AlertType.Warning.value,
  expression: applicationExpressions[0].value,
  netValueStopLossType: NavValueStopLossType.WARNING_LINE.value,
  value: 0,
});

const { ruleSetting } = defineProps<{ ruleSetting: RuleInnerSetting | null }>();

const rules = {
  expression: [{ required: true, message: '请选择比较符', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  () => {
    if (ruleSetting) {
      localRuleSetting.value = deepClone(ruleSetting);
    }
  },
  { immediate: true },
);

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  return `暂无`;
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="marketValueType">
          <label class="placed-label" pr-5>当净值</label>
          <el-select
            v-model="localRuleSetting.expression"
            style="width: 100px; margin-right: 5px"
            @change="handleParamHotChange"
          >
            <el-option
              v-for="(item, idx) in applicationExpressions"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-input-number
            :controls="false"
            :precision="3"
            :step="0.001"
            :min="0"
            v-model="localRuleSetting.value"
            @change="handleParamHotChange"
            style="width: 100px; margin-right: 5px"
          ></el-input-number>
          <el-select
            v-model="localRuleSetting.alertType"
            style="width: 140px"
            @change="handleParamHotChange"
          >
            <el-option
              v-for="(item, idx) in applicationAlertTypes"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
