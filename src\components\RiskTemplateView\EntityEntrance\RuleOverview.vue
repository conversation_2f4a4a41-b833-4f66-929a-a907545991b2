<script lang="ts" setup>
import SingleRuleRow from './SingleRuleRow.vue';
import { computed, onMounted, ref } from 'vue';
import type { TreeInstance } from 'element-plus';
import { ElTree } from 'element-plus';
import {
  Repos,
  type EntityRiskTemplateInfo,
  type RiskIndicator,
} from '../../../../../xtrade-sdk/dist';
import type { ContextualIndicatorInfo } from '@/types/riskc';
import { buildTree, isLeafNode, type LeafNode, type Level1Node } from '../IndicatorTreeNodeLogic';

const repoInstance = new Repos.RiskControlRepo();
// 根据设计图生成树形数据
const treeNodes = ref<Level1Node[]>();
const $treeRef = ref<TreeInstance>();
const templates = ref<EntityRiskTemplateInfo[]>([]);
const allRules = computed(() => {
  return templates.value.map(item => item.ruleList).flat();
});

async function request() {
  const indicators = (await repoInstance.QueryIndicators()).data || [];
  treeNodes.value = buildTree(indicators);
  const id2Names = indicators.map(x => ({
    indicatorId: x.id,
    indicatorName: x.indicatorName,
    componentName: x.clientName,
  }));
  emitter('report', id2Names);
}

const emitter = defineEmits<{
  report: [indicators: ContextualIndicatorInfo[]];
  select: [item: RiskIndicator];
}>();

defineExpose({
  //
});

onMounted(() => {
  request();
});
</script>

<template>
  <div class="tree-control" h-500 p-10>
    <!-- 树形结构 -->
    <el-tree
      ref="$treeRef"
      empty-text="无指标数据"
      node-key="id"
      :props="{ label: 'name', children: 'children' }"
      :data="treeNodes"
      :show-checkbox="false"
      highlight-current
      default-expand-all
    >
      <template #default="{ data }">
        <template v-if="isLeafNode(data)">
          <div class="leaf-name">{{ data.name }}</div>
          <div class="idc-rule-box">
            <SingleRuleRow :rules="[]"></SingleRuleRow>
          </div>
        </template>
        <span v-else class="level-name">{{ data.name }}</span>
      </template>
    </el-tree>
  </div>
</template>

<style scoped>
.tree-control {
  :deep() {
    .el-tree-node__label {
      font-size: 14px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }

    .el-tree-node__content:hover {
      background-color: var(--g-block-bg-6);
    }

    .el-tree-node {
      &.is-current {
        > .el-tree-node__content {
          background-color: var(--g-block-bg-6) !important;
        }
      }
    }

    .el-tree-node__content {
      display: flex;
      align-items: center;
    }

    .leaf-name {
      width: 260px;
    }

    .idc-rule-box {
      flex-grow: 1;
      flex-shrink: 1;
      min-width: 800px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
