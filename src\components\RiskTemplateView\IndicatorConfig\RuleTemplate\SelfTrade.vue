<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { deepClone, renderLabel } from '@/script';
import type { AnyIndicatorRiskParamObject } from '@/types/riskc';
import { AlertType, AlertTypes } from '@/enum/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';

/**
 * 自成交控制
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  /** 自成交反向 */
  selfTradeAlert: number;
}

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>({
  classType: IdcComponentNameDef.SelfTrade,
  selfTradeAlert: AlertType.Warning.value,
});

const { ruleSetting } = defineProps<{ ruleSetting: RuleInnerSetting | null }>();

const rules = {
  selfTradeAlert: [{ required: true, message: '请选择', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  () => {
    if (ruleSetting) {
      localRuleSetting.value = deepClone(ruleSetting);
    }
  },
  { immediate: true },
);

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  const { selfTradeAlert } = localRuleSetting.value;
  return `自成交「${renderLabel(selfTradeAlert, AlertTypes)}」；`;
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="selfTradeAlert">
          <span pr-5>自成交挂单</span>
          <el-select
            v-model="localRuleSetting.selfTradeAlert"
            style="width: 100px"
            @change="handleParamHotChange"
          >
            <el-option
              v-for="(item, idx) in AlertTypes"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
