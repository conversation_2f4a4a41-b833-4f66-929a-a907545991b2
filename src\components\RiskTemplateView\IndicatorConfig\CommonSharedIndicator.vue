<script setup lang="ts">
import { deepClone, isNone } from '@/script';
import ApplyAssetScope from './ApplyAssetScope.vue';
import { Repos, type RiskRule } from '../../../../../xtrade-sdk/dist';
import { ElMessage } from 'element-plus';
import { RiskStepControl } from '@/enum/riskc';
import type { ContextualIndicatorInfo } from '@/types/riskc';
import { IdcComponentNameDef } from '../ComponentNameDef';

import {
  computed,
  defineAsyncComponent,
  reactive,
  ref,
  useTemplateRef,
  watch,
  nextTick,
} from 'vue';

const indicatorDesignComponentsMap: Record<string, unknown> = {
  [IdcComponentNameDef.BlackList]: defineAsyncComponent(
    () => import('./RuleTemplate/BlackList.vue'),
  ),
  [IdcComponentNameDef.WhiteList]: defineAsyncComponent(
    () => import('./RuleTemplate/WhiteList.vue'),
  ),
  [IdcComponentNameDef.SingleOrderMaxVolume]: defineAsyncComponent(
    () => import('./RuleTemplate/SingleOrderMaxVolume.vue'),
  ),
  [IdcComponentNameDef.AccountOrderMaxVolume]: defineAsyncComponent(
    () => import('./RuleTemplate/AccountTotalOrderVolume.vue'),
  ),
  [IdcComponentNameDef.SingleOrderMaxAmount]: defineAsyncComponent(
    () => import('./RuleTemplate/SingleOrderMaxAmount.vue'),
  ),
  [IdcComponentNameDef.NetBuyAmount]: defineAsyncComponent(
    () => import('./RuleTemplate/NetBuyAmount.vue'),
  ),
  [IdcComponentNameDef.TradeFrequency]: defineAsyncComponent(
    () => import('./RuleTemplate/TradeFrequencyControl.vue'),
  ),
  [IdcComponentNameDef.OrderCancelRate]: defineAsyncComponent(
    () => import('./RuleTemplate/CancelOrderRateLimit.vue'),
  ),
  [IdcComponentNameDef.InvalidOrderRate]: defineAsyncComponent(
    () => import('./RuleTemplate/InvalidOrderRateLimit.vue'),
  ),
  [IdcComponentNameDef.IndayReversedDirection]: defineAsyncComponent(
    () => import('./RuleTemplate/IndayReversedDirection.vue'),
  ),
  [IdcComponentNameDef.SelfTrade]: defineAsyncComponent(
    () => import('./RuleTemplate/SelfTrade.vue'),
  ),
  [IdcComponentNameDef.PriceDeviation]: defineAsyncComponent(
    () => import('./RuleTemplate/PriceDeviation.vue'),
  ),
  [IdcComponentNameDef.PriceLimit]: defineAsyncComponent(
    () => import('./RuleTemplate/PriceLimit.vue'),
  ),
  [IdcComponentNameDef.MarketValue]: defineAsyncComponent(
    () => import('./RuleTemplate/MarketValue.vue'),
  ),
  [IdcComponentNameDef.MarketValueRatio]: defineAsyncComponent(
    () => import('./RuleTemplate/MarketValueRatio.vue'),
  ),
  [IdcComponentNameDef.MarketCapitalRatio]: defineAsyncComponent(
    () => import('./RuleTemplate/MarketCapitalRatio.vue'),
  ),
  [IdcComponentNameDef.FlowableMarketCapitalRatio]: defineAsyncComponent(
    () => import('./RuleTemplate/FlowableMarketCapitalRatio.vue'),
  ),
  [IdcComponentNameDef.NavStopLoss]: defineAsyncComponent(
    () => import('./RuleTemplate/NavStopLoss.vue'),
  ),
  [IdcComponentNameDef.FuturesPositionRatio]: defineAsyncComponent(
    () => import('./RuleTemplate/FuturesPositionRatio.vue'),
  ),
  [IdcComponentNameDef.FuturesMargin]: defineAsyncComponent(
    () => import('./RuleTemplate/FuturesMargin.vue'),
  ),
};

const { contextRule, contextIndicator } = defineProps<{
  contextRule: RiskRule;
  contextIndicator: ContextualIndicatorInfo;
}>();

const designComponent = computed(() => {
  return indicatorDesignComponentsMap[contextIndicator.componentName as IdcComponentNameDef];
});

const hasAllSteps = computed(() => {
  const cname = contextIndicator.componentName;
  return (
    cname == IdcComponentNameDef.BlackList ||
    cname == IdcComponentNameDef.WhiteList ||
    cname == IdcComponentNameDef.MarketValue ||
    cname == IdcComponentNameDef.MarketValueRatio ||
    cname == IdcComponentNameDef.MarketCapitalRatio ||
    cname == IdcComponentNameDef.FlowableMarketCapitalRatio ||
    cname == IdcComponentNameDef.NavStopLoss ||
    cname == IdcComponentNameDef.FuturesMargin ||
    cname == IdcComponentNameDef.FuturesPositionRatio
  );
});

const hasAssetScopeLimit = computed(() => {
  return hasAllSteps.value;
});

const formStyle = computed(() => {
  return { width: hasAssetScopeLimit.value ? '530px' : '100%' };
});

const { instruction, entrusting, progressing, marketClosed } = RiskStepControl;
const RiskControlObjectCode = {
  AllStepsObject: instruction.value | entrusting.value | progressing.value | marketClosed.value,
  OnlyInstructionObject: instruction.value,
};

function initializeCheckObject() {
  const { AllStepsObject, OnlyInstructionObject } = RiskControlObjectCode;
  return hasAllSteps.value ? AllStepsObject : OnlyInstructionObject;
}

function createDefaultCheckObjects() {
  const options = hasAllSteps.value
    ? [instruction, entrusting, progressing, marketClosed]
    : [entrusting];
  return options.map(x => ({ ...x, checked: true }));
}

function reconstructSteps(flag: number) {
  const results = createDefaultCheckObjects();
  results.map(x => {
    if ((flag & x.value) > 0) {
      x.checked = true;
    } else {
      x.checked = false;
    }
  });
  return results;
}

const formName = reactive({ name: '' });
const formData = reactive({
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  stepInfos: createDefaultCheckObjects(),
  stepInfo: {
    checkObject: initializeCheckObject(),
    checkInterval: 60,
  },
});

const $form = useTemplateRef('$form');
const $formName = useTemplateRef('$formName');
const $riskParam = useTemplateRef('$riskParam');
const $scope = useTemplateRef('$scope');

const rulesName = {
  name: [{ required: true, message: '请输入指标名字', trigger: 'blur' }],
};

const rules = {
  startDate: [{ required: false, message: '请输入开始日期', trigger: 'blur' }],
  startTime: [{ required: false, message: '请输入开始时间', trigger: 'blur' }],
  endDate: [
    { required: false, message: '请输入结束日期', trigger: 'blur' },
    { validator: checkEndDate, trigger: 'blur' },
  ],
  endTime: [
    { required: false, message: '请输入结束时间', trigger: 'blur' },
    { validator: checkEndTime, trigger: 'blur' },
  ],
};

const is4Creation = computed(() => {
  return isNone(contextRule.id);
});

const scopeConfig = computed(() => {
  const { kindCodes, baseConditions } = contextRule.configuration;
  return { kindCodes, overlaies: baseConditions.map(x => x.id) };
});

watch(() => contextRule, handleConextRuleChange, { immediate: true });

function handleConextRuleChange() {
  resetFormData();
  nextTick(() => {
    pullSummary();
  });
  setTimeout(() => {
    pullSummary();
  }, 500);
}
function resetFormData() {
  const { ruleName, beginDay, beginTime, endDay, endTime } = contextRule;
  const { checkInterval, checkObject } = contextRule;
  const fd = formData;
  formName.name = ruleName;

  fd.stepInfo.checkObject = checkObject;
  fd.stepInfo.checkInterval = checkInterval;
  fd.stepInfos = reconstructSteps(checkObject);

  fd.startDate = beginDay.toString();
  fd.endDate = endDay.toString();
  let t_start = beginTime.toString();
  let t_end = endTime.toString();

  while (t_start.length < 6) {
    t_start = '0' + t_start;
  }

  while (t_end.length < 6) {
    t_end = '0' + t_end;
  }

  fd.startTime = t_start;
  fd.endTime = t_end;
}

function checkEndDate(rule: any, value: string, callback: (error?: Error) => void) {
  const { startDate, endDate } = formData;
  if (startDate && endDate && endDate < startDate) {
    callback(new Error('结束日期，大于开始日期'));
  } else {
    callback();
  }
}

function checkEndTime(rule: any, value: string, callback: (error?: Error) => void) {
  const { startTime, endTime } = formData;
  if (startTime && endTime && endTime <= startTime) {
    callback(new Error('结束时间，大于等于开始时间'));
  } else {
    callback();
  }
}

const handleValidate = async () => {
  let r1 = true,
    r2 = true,
    r3 = true,
    r4 = true;
  try {
    await $formName.value!.validate();
  } catch (ex) {
    console.error(ex);
    r1 = false;
  }
  try {
    await $form.value!.validate();
  } catch (ex) {
    console.error(ex);
    r2 = false;
  }
  try {
    await ($riskParam.value! as any).validate();
  } catch (ex) {
    console.error(ex);
    r3 = false;
  }
  try {
    if ($scope.value) {
      await $scope.value!.validate();
    }
  } catch (ex) {
    console.error(ex);
    r4 = false;
  }

  if (r1 && r2 && r3 && r4) {
    handleSave();
  }
};

function changeCheckInterval() {
  const value = formData.stepInfo.checkInterval;
  if (typeof value !== 'number' || value < 60) {
    formData.stepInfo.checkInterval = 60;
  }
}

const riskSummary = ref<string | null>(null);

function handleRiskParamChanged(summary: string) {
  riskSummary.value = summary;
}

function pullSummary() {
  const $component = $riskParam.value as any;
  if (!$component) {
    return;
  }

  const summary = $component.getRiskParamSummary();
  riskSummary.value = summary;
}

const repoInstance = new Repos.RiskControlRepo();
const emitter = defineEmits<{
  saved: [];
}>();

const handleSave = async () => {
  // 资产分类范围
  const scopeSetting = $scope.value ? $scope.value!.getSetting() : { kindCodes: [], overlaies: [] };
  const { kindCodes, overlaies } = scopeSetting;

  // 风控规则详细配置
  let riskParam = ($riskParam.value! as any).getRiskSetting();
  const riskTypePropName = 'classType';
  const lastRiskType = (contextRule.configuration.riskParam || {})[riskTypePropName];
  const riskType = lastRiskType || contextIndicator.componentName;
  // 以下两行操作，为了解决后端接口未知的字段顺序导致的无法保存问题
  delete riskParam[riskTypePropName];
  riskParam = { [riskTypePropName]: riskType, ...riskParam };

  // 创建规则副本用于创建
  const cloned = deepClone(contextRule);
  // 设置其依附指标ID
  cloned.indicatorId = contextIndicator.indicatorId;
  // 更新规则名称
  cloned.ruleName = formName.name;

  // 更新其他附属字段
  const { startDate, endDate, startTime, endTime, stepInfo, stepInfos } = formData;
  cloned.beginDay = Number(startDate);
  cloned.endDay = Number(endDate);
  cloned.beginTime = Number(startTime);
  cloned.endTime = Number(endTime);
  cloned.checkInterval = stepInfo.checkInterval;

  // 构造控制环节标识
  const checkValues = stepInfos.filter(x => x.checked === true).map(x => x.value);
  let flag = checkValues.length > 0 ? 1 : 0;
  checkValues.forEach(each_value => {
    flag = flag | each_value;
  });
  cloned.checkObject = flag;

  const simpleConditions = overlaies.map(x => {
    const { id, conditionColumn: name, expressionType: expression, value } = x;
    return { id, name, expression, value };
  });

  // 组合规则的参数配置
  cloned.configuration = {
    indicatorId: contextIndicator.indicatorId,
    kindCodes,
    baseConditions: simpleConditions as any[],
    riskParam,
  };

  const resp = is4Creation.value
    ? await repoInstance.CreateRule(cloned)
    : await repoInstance.UpdateRule(cloned);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('保存成功');
    emitter('saved');
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
};
</script>

<template>
  <div class="view-idc-panel" h-full pt-5 p-x-12 flex flex-col gap-10>
    <div h-40>
      <el-form ref="$formName" :model="formName" :rules="rulesName" label-width="80px">
        <el-form-item label="指标名称" prop="name">
          <div class="name-row" w-full flex jcsb aic>
            <el-input v-model.trim="formName.name" placeholder="请输入指标名称" clearable />
            <el-button type="primary" @click="handleValidate" ml-10>
              {{ is4Creation ? '创建' : '更新' }}
            </el-button>
            <!-- <el-button>新增</el-button> -->
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div pt-10 flex-1 flex of-hidden>
      <div h-full of-y-auto pb-10 :style="formStyle">
        <el-form ref="$form" :model="formData" :rules="rules" label-width="80px">
          <KeepAlive>
            <component
              ref="$riskParam"
              :is="designComponent"
              v-bind:ruleSetting="contextRule.configuration.riskParam"
              @risk-param-changed="handleRiskParamChanged"
            ></component>
          </KeepAlive>
          <template v-for="(step, step_idx) in formData.stepInfos" :key="step_idx">
            <el-form-item :label="step_idx == 0 ? '控制环节' : ''">
              <div flex aic gap-10>
                <el-checkbox v-model="step.checked" :disabled="!hasAllSteps">
                  {{ step.label }}
                </el-checkbox>
                <template v-if="step.value == progressing.value">
                  <el-input-number
                    v-model="formData.stepInfo.checkInterval"
                    @change="changeCheckInterval"
                    :controls="false"
                    :precision="0"
                    :step="1"
                    :min="60"
                    style="width: 200px"
                  >
                    <template #prefix>
                      <span>每</span>
                    </template>
                    <template #suffix>
                      <span>秒执行一次</span>
                    </template>
                  </el-input-number>
                </template>
                <template v-else-if="step.value == marketClosed.value">
                  <el-input value="16点执行一次" style="width: 200px" disabled></el-input>
                </template>
              </div>
            </el-form-item>
          </template>
          <div class="custom-row" flex aic gap-16>
            <div w-270>
              <el-form-item label="生效日期" prop="startDate">
                <el-date-picker
                  v-model="formData.startDate"
                  type="date"
                  value-format="YYYYMMDD"
                  placeholder="开始日期"
                  clearable
                />
              </el-form-item>
            </div>
            <label class="placed-label">至</label>
            <div w-190 class="post-item">
              <el-form-item label="" prop="endDate">
                <el-date-picker
                  v-model="formData.endDate"
                  type="date"
                  value-format="YYYYMMDD"
                  placeholder="结束日期"
                  clearable
                />
              </el-form-item>
            </div>
          </div>
          <div class="custom-row" flex aic gap-16>
            <div w-270>
              <el-form-item label="生效时间" prop="startTime">
                <el-time-picker
                  v-model="formData.startTime"
                  value-format="HHmmss"
                  placeholder="开始时间"
                  clearable
                />
              </el-form-item>
            </div>
            <label class="placed-label">至</label>
            <div w-190 class="post-item">
              <el-form-item label="" prop="endTime">
                <el-time-picker
                  v-model="formData.endTime"
                  value-format="HHmmss"
                  placeholder="结束时间"
                  clearable
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
        <div class="risk-param-summary" fs-14 fw-400>
          <div class="inner-box" h-full w-full>
            <div>指标设置：</div>
            <br />
            <div>{{ riskSummary || '暂无' }}</div>
          </div>
        </div>
      </div>
      <template v-if="hasAllSteps">
        <div h-full flex-1 of-hidden>
          <ApplyAssetScope ref="$scope" :scope="scopeConfig"></ApplyAssetScope>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.view-idc-panel {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .name-row {
    :deep() {
      > .el-input {
        width: 100px;
        flex-grow: 1;
        flex-shrink: 1;
      }
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .ctr-check-time {
    :deep() {
      .el-form-item__label {
        width: 55px !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }

  .risk-param-summary {
    padding: 10px 12px;
    .inner-box {
      padding: 10px 12px;
      border-radius: 6px;
      color: var(--g-text-color-6);
      border: 1px solid var(--g-panel-bg);
    }
  }
}
</style>

<style>
.view-idc-panel {
  .el-form-item__label {
    height: 40px;
    line-height: 40px;
  }

  .el-select,
  .el-select__wrapper,
  .el-input {
    height: 40px;
  }
}
</style>
