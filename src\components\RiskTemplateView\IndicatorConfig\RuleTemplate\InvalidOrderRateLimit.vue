<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { deepClone, isNone, renderLabel } from '@/script';
import type { AnyIndicatorRiskParamObject, CommonRiskAlertConfig } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';

import {
  AlertType,
  AlertTypes,
  ExpressionType,
  ExpressionTypes,
  RiskStatisticsType,
} from '@/enum/riskc';

/**
 * 废单比率限制
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  expression: number;
  // 委托次数
  orderCount: number;
  // 单票还是汇总
  riskStatisticsType: number;
  paramAlert: CommonRiskAlertConfig;
  paramForbidOrder: CommonRiskAlertConfig;
}

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>({
  classType: IdcComponentNameDef.InvalidOrderRate,
  expression: ExpressionType.GreaterThan.value,
  orderCount: 500,
  riskStatisticsType: RiskStatisticsType.Summary.value,
  paramAlert: {
    alertType: AlertType.Warning.value,
    expression: ExpressionType.GreaterThan.value,
    value: 20,
  },
  paramForbidOrder: {
    alertType: AlertType.ForbiddenOfCancel.value,
    expression: ExpressionType.GreaterThan.value,
    value: 30,
  },
});

const definedActions = computed(() => {
  const { paramAlert, paramForbidOrder } = localRuleSetting.value as RuleInnerSetting;
  return [paramAlert, paramForbidOrder];
});

const { ruleSetting } = defineProps<{ ruleSetting: RuleInnerSetting | null }>();

const rules = {
  actions: [
    { required: true, message: '请选择比较符', trigger: 'blur', validator: validateActions },
  ],
};

function validateActions(rule: any, value: any, callback: any) {
  const failed = definedActions.value.some(item => {
    const { expression, value, alertType } = item;
    if (isNone(expression) || isNone(value) || isNone(alertType)) {
      return true;
    } else {
      return false;
    }
  });
  if (failed) {
    callback(new Error('请设置完善的风控条件'));
  } else {
    callback();
  }
}

watch(
  () => ruleSetting,
  () => {
    if (ruleSetting) {
      localRuleSetting.value = deepClone(ruleSetting);
    }
  },
  { immediate: true },
);

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  const { expression, orderCount, paramAlert, paramForbidOrder } = localRuleSetting.value;
  return `「委托次数」「${expression}${orderCount}次时」，废单比率按汇总限制；
  「${paramAlert.expression}${paramAlert.value}%时」${renderLabel(paramAlert.alertType, AlertTypes)};
  「${paramForbidOrder.expression}${paramForbidOrder.value}%时」${renderLabel(paramForbidOrder.alertType, AlertTypes)}；`;
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <!-- <el-alert type="error" effect="dark" :closable="false" title="盘中不支持修改该指标" /> -->
      </div>
      <div class="custom-row">
        <el-form-item label="指标设置" prop="expression">
          <div w-full flex aic gap-10>
            <div w-auto>
              <label class="placed-label">委托次数</label>
            </div>
            <el-select
              v-model="localRuleSetting.expression"
              style="width: 100px"
              @change="handleParamHotChange"
            >
              <el-option
                v-for="(item, idx) in ExpressionTypes"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-input-number
              :controls="false"
              :precision="0"
              :step="1"
              :min="0"
              v-model="localRuleSetting.orderCount"
              @change="handleParamHotChange"
              style="width: 80px"
            ></el-input-number>
            <label class="placed-label">次时，废单比率按汇总限制</label>
          </div>
        </el-form-item>
      </div>
      <template v-for="(item, idx) in definedActions" :key="idx">
        <div class="custom-row">
          <el-form-item label="" prop="expression">
            <div w-full flex aic gap-10>
              <el-select
                v-model="item.expression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in ExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="0"
                :step="1"
                :min="0"
                :max="100"
                v-model="item.value"
                @change="handleParamHotChange"
                style="width: 80px"
              ></el-input-number>
              <label class="placed-label">%时，</label>
              <el-select
                v-model="item.alertType"
                style="width: 140px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in AlertTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </div>
      </template>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
