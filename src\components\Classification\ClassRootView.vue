<script setup lang="ts">
import ClassView from './ClassView.vue';
import ClassMemberView from './ClassMemberView.vue';
import { ref } from 'vue';
import { TradeClassificationType, TradeClassificationTypes } from '../../../../xtrade-sdk/dist';

const classType = ref(TradeClassificationType.Asset);
const classId = ref<number | null>(null);
const className = ref<string | null>(null);

function handleClassChange(new_class_id: number | null, new_class_name: string | null) {
  classId.value = new_class_id;
  className.value = new_class_name;
}
</script>

<template>
  <div class="class-root-view" h-full w-full flex>
    <div w-500 flex-1 of-y-hidden>
      <div h-50 p-b-12>
        <el-tabs v-model="classType">
          <el-tab-pane
            v-for="item in TradeClassificationTypes"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <ClassView :class-type="classType" @class-change="handleClassChange"></ClassView>
    </div>
    <div w-500 flex-1 of-y-hidden>
      <ClassMemberView
        :class-type="classType"
        :class-id="classId"
        :class-name="className"
      ></ClassMemberView>
    </div>
  </div>
</template>

<style scoped></style>
